import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthController } from './modules/auth/auth.controller';
import { AuthModule } from './modules/auth/auth.module';
import { BrokerModule } from './modules/broker/broker.module';
import { CalculatorModule } from './modules/calculator/calculator.module';
import { ManageAccountsModule } from './modules/manage-accounts/manage-accounts.module';
import { ProfitModule } from './modules/profit/profit.module';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtAuthGuard } from './Gaurd/jwt-auth.guard';
import { SupportModule } from './modules/support/support.module';
import { UploadModule } from './modules/upload/upload.module';
import { BlogsModule } from './modules/blogs/blogs.module';
import { FaqsModule } from './modules/faqs/faqs.module';
import { WithdrawModule } from './modules/withdraw/withdraw.module';
import { ChatModule } from './modules/chat/chat.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { UserDashboardModule } from './modules/user-dashboard/user-dashboard.module';
import { NotificationsModule } from './modules/notifications/notifications.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    AuthModule,
    BrokerModule,
    CalculatorModule,
    ManageAccountsModule,
    ProfitModule,
    SupportModule,
    UploadModule,
    BlogsModule,
    FaqsModule,
    WithdrawModule,
    ChatModule,
    DashboardModule,
    UserDashboardModule,
    NotificationsModule,
    MongooseModule.forRoot(
      'mongodb+srv://root:<EMAIL>/tradereward',
      {
        connectionFactory: (connection) => {
          console.log('✅ Database connected successfully!');
          console.log('📊 Database name:', connection.db.databaseName);
          return connection;
        },
      },
    ),
  ],
  controllers: [AppController, AuthController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
})
export class AppModule {}
