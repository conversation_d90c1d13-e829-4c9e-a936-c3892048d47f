import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ManageAccount } from './entities/manage-account.entity';
import { Broker } from '../broker/entity/broker';
import { Auth } from '../auth/entities/auth.entity';
import { JoinBrokerDto } from './dto/create-manage-account.dto';

@Injectable()
export class ManageAccountsService {
  constructor(
    @InjectModel(ManageAccount.name)
    private manageAccountModel: Model<ManageAccount>,
    @InjectModel(Broker.name) private brokerModel: Model<Broker>,
    @InjectModel(Auth.name) private authModel: Model<Auth>,
  ) {}

  async joinBroker(
    joinBrokerDto: JoinBrokerDto,
    userId: string,
  ): Promise<ManageAccount> {
    try {
      // Check if broker exists
      const broker = await this.brokerModel.findById(joinBrokerDto.brokerId);
      if (!broker) {
        throw new NotFoundException('Broker not found');
      }

      // Check if user exists
      const user = await this.authModel.findById(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if user already joined this broker
      const existingAccount = await this.manageAccountModel.findOne({
        userId: userId,
        brokerId: joinBrokerDto.brokerId,
        accountNo: joinBrokerDto.accountNo,
      });

      if (existingAccount) {
        throw new ConflictException(
          'User already joined this broker with this account number',
        );
      }

      // Check if account number already exists for this broker
      const existingAccountNo = await this.manageAccountModel.findOne({
        accountNo: joinBrokerDto.accountNo,
        brokerId: joinBrokerDto.brokerId,
      });

      if (existingAccountNo) {
        throw new ConflictException(
          'Account number already exists for this broker',
        );
      }

      // Create new account
      const newAccount = new this.manageAccountModel({
        accountNo: joinBrokerDto.accountNo,
        userId: userId,
        brokerId: joinBrokerDto.brokerId,
        firstname: user.firstName,
        lastname: user.lastName,
        accountType: joinBrokerDto.accountType,
        // exchangeTitle:joinBrokerDto.exchangeTitle,
        // referralLink:joinBrokerDto.referralLink
      });

      await newAccount.save();
      user.connectedBrokers.push({
        brokerId: new Types.ObjectId(broker._id as string),
        accountId: new Types.ObjectId(newAccount._id as string),
        accountType: joinBrokerDto.accountType,
      });
      await user.save();

      // Add user to broker's connectedUsers array
      await this.brokerModel.findByIdAndUpdate(
        joinBrokerDto.brokerId,
        { 
          $addToSet: { 
            connectedUsers: {
              userId: userId,
              accountId: newAccount._id,
              accountNo: joinBrokerDto.accountNo,
              accountType: joinBrokerDto.accountType
            }
          } 
        },
        { new: true },
      );

      return newAccount;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(`Failed to join broker: ${error.message}`);
    }
  }

  async getUserAccounts(userId: string): Promise<ManageAccount[]> {
    try {
      return await this.manageAccountModel
        .find({ userId })
        .populate('brokerId', 'brokerName brokerType brokerImage')
        .exec();
    } catch (error) {
      throw new BadRequestException(
        `Failed to fetch user accounts: ${error.message}`,
      );
    }
  }

  async getBrokerAccounts(brokerId: string): Promise<ManageAccount[]> {
    try {
      return await this.manageAccountModel
        .find({ brokerId })
        .populate('userId', 'firstName lastName email')
        .exec();
    } catch (error) {
      throw new BadRequestException(
        `Failed to fetch broker accounts: ${error.message}`,
      );
    }
  }

  async getAccountsByUserId(targetUserId: string, adminId: string): Promise<ManageAccount[]> {
    console.log('📋 Admin getting accounts for user:', targetUserId);

    try {
      // Check if requester is admin
      const admin = await this.authModel.findById(adminId);
      if (!admin || admin.role !== 'admin') {
        throw new BadRequestException('Only admin can view user accounts');
      }

      // Check if target user exists
      const targetUser = await this.authModel.findById(targetUserId);
      if (!targetUser) {
        throw new NotFoundException('Target user not found');
      }

      if (targetUser.role === 'admin') {
        throw new BadRequestException('Cannot view admin user accounts');
      }

      // Get all accounts for the target user
      const accounts = await this.manageAccountModel
        .find({ userId: targetUserId })
        .populate('brokerId', 'brokerName brokerType brokerImage')
        .populate('userId', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .exec();

      console.log(`✅ Found ${accounts.length} accounts for user ${targetUserId}`);
      return accounts;

    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch user accounts: ${error.message}`,
      );
    }
  }
}
