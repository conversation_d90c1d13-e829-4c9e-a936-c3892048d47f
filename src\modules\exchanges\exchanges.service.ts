import { Injectable } from '@nestjs/common';
import { ExchangeDto } from './dto/exchange.dto';


@Injectable()
export class ExchangesService {
  create(ExchangeDto: ExchangeDto) {
    return 'This action adds a new exchange';
  }

  findAll() {
    return `This action returns all exchanges`;
  }

  findOne(id: number) {
    return `This action returns a #${id} exchange`;
  }

  update(id: number, ExchangeDto: ExchangeDto) {
    return `This action updates a #${id} exchange`;
  }

  remove(id: number) {
    return `This action removes a #${id} exchange`;
  }
}
