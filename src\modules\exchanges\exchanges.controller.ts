import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ExchangesService } from './exchanges.service';
import { ExchangeDto } from './dto/exchange.dto';


@Controller('exchanges')
export class ExchangesController {
  constructor(private readonly exchangesService: ExchangesService) {}

  @Post()
  create(@Body() ExchangeDto: ExchangeDto) {
    return this.exchangesService.create(ExchangeDto);
  }

  @Get()
  findAll() {
    return this.exchangesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.exchangesService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() ExchangeDto: ExchangeDto) {
    return this.exchangesService.update(+id, ExchangeDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.exchangesService.remove(+id);
  }
}
